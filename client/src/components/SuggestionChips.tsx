import {
  ArrowRightIcon,
  HelpCircleIcon,
  CodeIcon,
  DollarSignIcon,
  SearchIcon,
  SendIcon,
} from "lucide-react";

interface SuggestionChipsProps {
  onChipClick: (suggestion: string) => void;
}

const suggestions = [
  "What Web3AI can do?",
  "Create an ERC-20 token",
  "Buy stablecoins",
  "Audit smart contracts",
  "Check gas prices",
];

const SuggestionChips = ({ onChipClick }: SuggestionChipsProps) => {
  return (
    <div className="flex flex-wrap justify-center gap-2 sm:gap-3 mb-4 px-2">
      {suggestions.map((suggestion, index) => (
        <button
          key={index}
          className="nebula-suggestion-chip nebula-hover text-xs sm:text-sm min-h-[36px] px-2 sm:px-3 py-1 sm:py-2"
          onClick={() => onChipClick(suggestion)}
        >
          <span className="truncate">{suggestion}</span>
          <ArrowRightIcon className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-primary ml-1 flex-shrink-0" />
        </button>
      ))}
    </div>
  );
};

export default SuggestionChips;
