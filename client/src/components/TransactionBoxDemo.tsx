import { useState } from "react";
import TransactionBox from "@/components/TransactionBox";
import { Button } from "@/components/ui/button";

const TransactionBoxDemo = () => {
  const [transactionHash, setTransactionHash] = useState<string | null>(null);

  // Example transaction data
  const exampleTransaction = {
    from: "0x4ECb...b8c5",
    to: "0x2554...07a1", 
    value: "0", // 0 ETH
    chainId: 1, // Ethereum Mainnet
    data: "0x",
    gasLimit: "21000",
    gasPrice: "20000000000", // 20 gwei
  };

  // Mock execute function
  const handleExecuteTransaction = async (txData: any) => {
    console.log("Executing transaction:", txData);
    
    // Simulate transaction execution
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Mock transaction hash
    const mockHash = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef";
    setTransactionHash(mockHash);
    
    return mockHash;
  };

  const resetDemo = () => {
    setTransactionHash(null);
  };

  return (
    <div className="p-6 max-w-md mx-auto">
      <h2 className="text-lg font-semibold mb-4">TransactionBox Demo</h2>
      
      <TransactionBox
        title="Transaction"
        transactionData={exampleTransaction}
        onExecute={handleExecuteTransaction}
        transactionHash={transactionHash}
      />
      
      {transactionHash && (
        <Button 
          onClick={resetDemo}
          className="mt-4 w-full"
          variant="outline"
        >
          Reset Demo
        </Button>
      )}
      
      <div className="mt-6 p-4 bg-muted/30 rounded-md">
        <h3 className="text-sm font-medium mb-2">Usage Example:</h3>
        <pre className="text-xs text-muted-foreground overflow-x-auto">
{`<TransactionBox
  title="Transaction"
  transactionData={{
    from: "0x4ECb...b8c5",
    to: "0x2554...07a1",
    value: "0",
    chainId: 1,
  }}
  onExecute={handleExecute}
  transactionHash={txHash}
/>`}
        </pre>
      </div>
    </div>
  );
};

export default TransactionBoxDemo;
